name: Check

on:
  push:
    branches:
      - main

  pull_request:
    branches:
      - main

jobs:
  lint:
    strategy:
      matrix:
        node_version: [18.x, 20.x, 22.x]
        os: [ubuntu-latest, windows-latest, macos-latest]
      fail-fast: false

    runs-on: ${{ matrix.os }}

    timeout-minutes: 10

    steps:
      - run: |
          git config --global core.autocrlf false
          git config --global core.eol lf
      - uses: actions/checkout@v4
      - uses: pnpm/action-setup@v4
      - uses: actions/setup-node@v4
        with:
          node-version: ${{ matrix.node_version }}
          cache: pnpm
      - run: pnpm i
      - run: pnpm run lint

  typecheck:
    strategy:
      matrix:
        node_version: [18.x, 20.x, 22.x]
        os: [ubuntu-latest, windows-latest, macos-latest]
      fail-fast: false

    runs-on: ${{ matrix.os }}

    timeout-minutes: 10

    steps:
      - run: |
          git config --global core.autocrlf false
          git config --global core.eol lf
      - uses: actions/checkout@v4
      - uses: pnpm/action-setup@v4
      - uses: actions/setup-node@v4
        with:
          node-version: ${{ matrix.node_version }}
          cache: pnpm
      - run: pnpm i
      - run: pnpm run type-check

  build:
    strategy:
      matrix:
        node_version: [18.x, 20.x, 22.x]
        os: [ubuntu-latest, windows-latest, macos-latest]
        build_cmd: [build, 'build:mp-weixin', 'build:app']
      fail-fast: false

    runs-on: ${{ matrix.os }}

    timeout-minutes: 10

    steps:
      - run: |
          git config --global core.autocrlf false
          git config --global core.eol lf
      - uses: actions/checkout@v4
      - uses: pnpm/action-setup@v4
      - uses: actions/setup-node@v4
        with:
          node-version: ${{ matrix.node_version }}
          cache: pnpm
      - run: pnpm i
      - run: pnpm run ${{ matrix.build_cmd }}
