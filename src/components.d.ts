/* eslint-disable */
/* prettier-ignore */
// @ts-nocheck
// Generated by vite-plugin-uni-components
// Read more: https://github.com/vuejs/core/pull/3399
export {}

declare module 'vue' {
  export interface GlobalComponents {
    AppFooter: typeof import('./components/AppFooter.vue')['default']
    AppLogos: typeof import('./components/AppLogos.vue')['default']
    HiCounter: typeof import('./components/HiCounter.vue')['default']
    InputEntry: typeof import('./components/InputEntry.vue')['default']
  }
}
